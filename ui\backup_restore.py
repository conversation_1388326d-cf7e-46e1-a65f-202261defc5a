# -*- coding: utf-8 -*-
# cSpell:disable
"""
واجهة النسخ الاحتياطي والاستعادة
"""

import customtkinter as ctk
import tkinter as tk
from tkinter import messagebox, filedialog, ttk
import os
import threading
from datetime import datetime

from core.app_core import AppCore
from themes.theme_manager import ThemeManager
from ui.window_utils import configure_window_fullscreen

class BackupRestoreWindow:
    """نافذة النسخ الاحتياطي والاستعادة"""

    def __init__(self, parent):
        self.parent = parent
        self.app_core = AppCore()
        self.theme_manager = ThemeManager()
        self.window = None
        self.progress_var = None
        self.status_var = None

    def show(self):
        """عرض النافذة"""
        self.create_window()

    def create_window(self):
        """إنشاء النافذة"""
        self.window = ctk.CTkToplevel(self.parent)
        
        # إعداد النافذة لتملأ الشاشة
        configure_window_fullscreen(self.window, "النسخ الاحتياطي والاستعادة - برنامج ست الكل للمحاسبة")
        self.window.transient(self.parent)
        self.window.grab_set()

        # توسيط النافذة
        self.center_window()

        # إنشاء المحتوى
        self.create_content()

    def center_window(self):
        """توسيط النافذة"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f"{width}x{height}+{x}+{y}")

    def create_content(self):
        """إنشاء محتوى النافذة"""
        # الإطار الرئيسي
        main_frame = self.theme_manager.create_styled_frame(self.window)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # العنوان
        title_label = self.theme_manager.create_styled_label(
            main_frame,
            text="النسخ الاحتياطي والاستعادة",
            font_size=18
        )
        title_label.pack(pady=(0, 20))

        # إنشاء التبويبات
        self.create_tabs(main_frame)

        # شريط الحالة والتقدم
        self.create_status_bar(main_frame)

        # أزرار الإغلاق
        self.create_buttons(main_frame)

    def create_tabs(self, parent):
        """إنشاء التبويبات"""
        # إطار التبويبات
        tabs_frame = ctk.CTkFrame(parent)
        tabs_frame.pack(fill="both", expand=True, pady=(0, 20))

        # إنشاء التبويبات
        self.tabview = ctk.CTkTabview(tabs_frame)
        self.tabview.pack(fill="both", expand=True, padx=10, pady=10)

        # تبويب النسخ الاحتياطي
        self.backup_tab = self.tabview.add("النسخ الاحتياطي")
        self.create_backup_tab()

        # تبويب الاستعادة
        self.restore_tab = self.tabview.add("الاستعادة")
        self.create_restore_tab()

        # تبويب الجدولة
        self.schedule_tab = self.tabview.add("الجدولة التلقائية")
        self.create_schedule_tab()

    def create_backup_tab(self):
        """إنشاء تبويب النسخ الاحتياطي"""
        # إطار المعلومات
        info_frame = self.theme_manager.create_styled_frame(self.backup_tab)
        info_frame.pack(fill="x", padx=10, pady=10)

        info_label = self.theme_manager.create_styled_label(
            info_frame,
            text="إنشاء نسخة احتياطية من قاعدة البيانات والإعدادات",
            font_size=12
        )
        info_label.pack(pady=10)

        # خيارات النسخ الاحتياطي
        options_frame = self.theme_manager.create_styled_frame(self.backup_tab)
        options_frame.pack(fill="x", padx=10, pady=10)

        options_title = self.theme_manager.create_styled_label(
            options_frame,
            text="خيارات النسخ الاحتياطي:",
            font_size=14
        )
        options_title.pack(anchor="e", padx=10, pady=(10, 5))

        # خيارات الاختيار
        self.backup_database = tk.BooleanVar(value=True)
        self.backup_settings = tk.BooleanVar(value=True)
        self.backup_reports = tk.BooleanVar(value=False)

        db_check = ctk.CTkCheckBox(
            options_frame,
            text="قاعدة البيانات",
            variable=self.backup_database
        )
        db_check.pack(anchor="e", padx=20, pady=5)

        settings_check = ctk.CTkCheckBox(
            options_frame,
            text="الإعدادات",
            variable=self.backup_settings
        )
        settings_check.pack(anchor="e", padx=20, pady=5)

        reports_check = ctk.CTkCheckBox(
            options_frame,
            text="التقارير المحفوظة",
            variable=self.backup_reports
        )
        reports_check.pack(anchor="e", padx=20, pady=5)

        # مسار الحفظ
        path_frame = self.theme_manager.create_styled_frame(self.backup_tab)
        path_frame.pack(fill="x", padx=10, pady=10)

        path_label = self.theme_manager.create_styled_label(
            path_frame,
            text="مسار الحفظ:",
            font_size=12
        )
        path_label.pack(anchor="e", padx=10, pady=(10, 5))

        path_input_frame = ctk.CTkFrame(path_frame, fg_color="transparent")
        path_input_frame.pack(fill="x", padx=10, pady=(0, 10))

        self.backup_path_var = tk.StringVar(value=str(Path.home() / "Desktop"))
        self.backup_path_entry = self.theme_manager.create_styled_entry(
            path_input_frame,
            textvariable=self.backup_path_var,
            width=400
        )
        self.backup_path_entry.pack(side="right", padx=(0, 10))

        browse_btn = self.theme_manager.create_styled_button(
            path_input_frame,
            text="تصفح",
            command=self.browse_backup_path,
            button_type="secondary",
            width=80,
            height=30
        )
        browse_btn.pack(side="right")

        # زر إنشاء النسخة الاحتياطية
        backup_btn = self.theme_manager.create_styled_button(
            self.backup_tab,
            text="إنشاء نسخة احتياطية",
            command=self.create_backup,
            button_type="primary",
            width=200,
            height=40,
            font_size=14
        )
        backup_btn.pack(pady=20)

    def create_restore_tab(self):
        """إنشاء تبويب الاستعادة"""
        # إطار المعلومات
        info_frame = self.theme_manager.create_styled_frame(self.restore_tab)
        info_frame.pack(fill="x", padx=10, pady=10)

        info_label = self.theme_manager.create_styled_label(
            info_frame,
            text="استعادة البيانات من نسخة احتياطية سابقة",
            font_size=12
        )
        info_label.pack(pady=10)

        warning_label = self.theme_manager.create_styled_label(
            info_frame,
            text="تحذير: ستؤدي الاستعادة إلى استبدال البيانات الحالية",
            font_size=11
        )
        warning_label.configure(text_color=self.theme_manager.get_color("error"))
        warning_label.pack(pady=5)

        # قائمة النسخ الاحتياطية المتاحة
        backups_frame = self.theme_manager.create_styled_frame(self.restore_tab)
        backups_frame.pack(fill="both", expand=True, padx=10, pady=10)

        backups_title = self.theme_manager.create_styled_label(
            backups_frame,
            text="النسخ الاحتياطية المتاحة:",
            font_size=14
        )
        backups_title.pack(anchor="e", padx=10, pady=(10, 5))

        # جدول النسخ الاحتياطية
        tree_frame = ctk.CTkFrame(backups_frame)
        tree_frame.pack(fill="both", expand=True, padx=10, pady=10)

        columns = ('اسم الملف', 'التاريخ', 'الحجم', 'المسار')
        self.backups_tree = ttk.Treeview(tree_frame, columns=columns, show='headings', height=8)

        for col in columns:
            self.backups_tree.heading(col, text=col)
            self.backups_tree.column(col, width=150, anchor='center')

        scrollbar_restore = ttk.Scrollbar(tree_frame, orient="vertical", command=self.backups_tree.yview)
        self.backups_tree.configure(yscrollcommand=scrollbar_restore.set)

        self.backups_tree.pack(side="right", fill="both", expand=True)
        scrollbar_restore.pack(side="left", fill="y")

        # أزرار الاستعادة
        restore_buttons_frame = ctk.CTkFrame(self.restore_tab, fg_color="transparent")
        restore_buttons_frame.pack(fill="x", padx=10, pady=10)

        refresh_btn = self.theme_manager.create_styled_button(
            restore_buttons_frame,
            text="تحديث القائمة",
            command=self.refresh_backups_list,
            button_type="secondary",
            width=120,
            height=35
        )
        refresh_btn.pack(side="right", padx=5)

        browse_restore_btn = self.theme_manager.create_styled_button(
            restore_buttons_frame,
            text="تصفح ملف",
            command=self.browse_restore_file,
            button_type="secondary",
            width=120,
            height=35
        )
        browse_restore_btn.pack(side="right", padx=5)

        restore_btn = self.theme_manager.create_styled_button(
            restore_buttons_frame,
            text="استعادة",
            command=self.restore_backup,
            button_type="primary",
            width=120,
            height=35
        )
        restore_btn.pack(side="right", padx=5)

        # تحميل قائمة النسخ الاحتياطية
        self.refresh_backups_list()

    def create_schedule_tab(self):
        """إنشاء تبويب الجدولة التلقائية"""
        # إطار المعلومات
        info_frame = self.theme_manager.create_styled_frame(self.schedule_tab)
        info_frame.pack(fill="x", padx=10, pady=10)

        info_label = self.theme_manager.create_styled_label(
            info_frame,
            text="إعداد النسخ الاحتياطي التلقائي",
            font_size=12
        )
        info_label.pack(pady=10)

        # إعدادات الجدولة
        schedule_frame = self.theme_manager.create_styled_frame(self.schedule_tab)
        schedule_frame.pack(fill="x", padx=10, pady=10)

        # تفعيل النسخ التلقائي
        self.auto_backup_enabled = tk.BooleanVar(
            value=self.app_core.get_setting('auto_backup', True)
        )

        enable_check = ctk.CTkCheckBox(
            schedule_frame,
            text="تفعيل النسخ الاحتياطي التلقائي",
            variable=self.auto_backup_enabled,
            command=self.toggle_auto_backup
        )
        enable_check.pack(anchor="e", padx=20, pady=10)

        # فترة النسخ الاحتياطي
        interval_label = self.theme_manager.create_styled_label(
            schedule_frame,
            text="فترة النسخ الاحتياطي (بالساعات):",
            font_size=12
        )
        interval_label.pack(anchor="e", padx=20, pady=(10, 5))

        self.backup_interval_var = tk.StringVar(
            value=str(self.app_core.get_setting('backup_interval', 24))
        )

        interval_entry = self.theme_manager.create_styled_entry(
            schedule_frame,
            textvariable=self.backup_interval_var,
            width=100,
            justify="center"
        )
        interval_entry.pack(padx=20, pady=(0, 10))

        # عدد النسخ المحفوظة
        keep_label = self.theme_manager.create_styled_label(
            schedule_frame,
            text="عدد النسخ الاحتياطية المحفوظة:",
            font_size=12
        )
        keep_label.pack(anchor="e", padx=20, pady=(10, 5))

        self.keep_backups_var = tk.StringVar(value="10")

        keep_entry = self.theme_manager.create_styled_entry(
            schedule_frame,
            textvariable=self.keep_backups_var,
            width=100,
            justify="center"
        )
        keep_entry.pack(padx=20, pady=(0, 10))

        # زر حفظ الإعدادات
        save_settings_btn = self.theme_manager.create_styled_button(
            schedule_frame,
            text="حفظ الإعدادات",
            command=self.save_schedule_settings,
            button_type="primary",
            width=150,
            height=35
        )
        save_settings_btn.pack(pady=20)

    def create_status_bar(self, parent):
        """إنشاء شريط الحالة والتقدم"""
        status_frame = self.theme_manager.create_styled_frame(parent, height=80)
        status_frame.pack(fill="x", pady=(0, 10))
        status_frame.pack_propagate(False)

        # شريط التقدم
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ctk.CTkProgressBar(
            status_frame,
            variable=self.progress_var,
            width=400,
            height=20
        )
        self.progress_bar.pack(pady=(10, 5))

        # نص الحالة
        self.status_var = tk.StringVar(value="جاهز")
        status_label = self.theme_manager.create_styled_label(
            status_frame,
            textvariable=self.status_var,
            font_size=11
        )
        status_label.pack()

    def create_buttons(self, parent):
        """إنشاء أزرار الإغلاق"""
        buttons_frame = ctk.CTkFrame(parent, fg_color="transparent")
        buttons_frame.pack(fill="x")

        close_btn = self.theme_manager.create_styled_button(
            buttons_frame,
            text="إغلاق",
            command=self.close_window,
            button_type="secondary",
            width=100,
            height=35
        )
        close_btn.pack(side="left")

    def browse_backup_path(self):
        """تصفح مسار النسخ الاحتياطي"""
        path = filedialog.askdirectory(
            title="اختر مجلد الحفظ",
            initialdir=self.backup_path_var.get()
        )
        if path:
            self.backup_path_var.set(path)

    def create_backup(self):
        """إنشاء نسخة احتياطية"""
        if not self.backup_database.get() and not self.backup_settings.get() and not self.backup_reports.get():
            messagebox.showwarning("تحذير", "يرجى اختيار عنصر واحد على الأقل للنسخ الاحتياطي")
            return

        # تشغيل النسخ الاحتياطي في خيط منفصل
        thread = threading.Thread(target=self._create_backup_thread)
        thread.daemon = True
        thread.start()

    def _create_backup_thread(self):
        """تنفيذ النسخ الاحتياطي في خيط منفصل"""
        try:
            self.update_status("بدء النسخ الاحتياطي...", 0)

            import zipfile
            import shutil

            # إنشاء اسم الملف
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_filename = f"backup_{timestamp}.zip"
            backup_path = Path(self.backup_path_var.get()) / backup_filename

            # إنشاء ملف ZIP
            with zipfile.ZipFile(backup_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                total_steps = sum([
                    self.backup_database.get(),
                    self.backup_settings.get(),
                    self.backup_reports.get()
                ])
                current_step = 0

                # نسخ قاعدة البيانات
                if self.backup_database.get():
                    self.update_status("نسخ قاعدة البيانات...", (current_step / total_steps) * 100)
                    from config.settings import DATABASE_PATH
                    if DATABASE_PATH.exists():
                        zipf.write(DATABASE_PATH, "database/accounting.db")
                    current_step += 1

                # نسخ الإعدادات
                if self.backup_settings.get():
                    self.update_status("نسخ الإعدادات...", (current_step / total_steps) * 100)
                    from config.settings import PROJECT_ROOT
                    config_dir = PROJECT_ROOT / "config"
                    if config_dir.exists():
                        for file_path in config_dir.rglob("*"):
                            if file_path.is_file():
                                arcname = f"config/{file_path.relative_to(config_dir)}"
                                zipf.write(file_path, arcname)
                    current_step += 1

                # نسخ التقارير
                if self.backup_reports.get():
                    self.update_status("نسخ التقارير...", (current_step / total_steps) * 100)
                    from config.settings import REPORTS_PATH
                    if REPORTS_PATH.exists():
                        for file_path in REPORTS_PATH.rglob("*"):
                            if file_path.is_file():
                                arcname = f"reports/{file_path.relative_to(REPORTS_PATH)}"
                                zipf.write(file_path, arcname)
                    current_step += 1

            self.update_status("تم إنشاء النسخة الاحتياطية بنجاح", 100)

            # عرض رسالة نجاح
            self.window.after(0, lambda: messagebox.showinfo(
                "نجح",
                f"تم إنشاء النسخة الاحتياطية بنجاح:\n{backup_path}"
            ))

        except Exception as e:
            pass
        except Exception as e:
            error_msg = f"خطأ في إنشاء النسخة الاحتياطية: {e}"
            self.update_status(error_msg, 0)
            self.window.after(0, lambda: messagebox.showerror("خطأ", error_msg))

    def refresh_backups_list(self):
        """تحديث قائمة النسخ الاحتياطية"""
        try:
            # مسح القائمة الحالية
            for item in self.backups_tree.get_children():
                self.backups_tree.delete(item)

            # البحث عن النسخ الاحتياطية
            backups_dir = PROJECT_ROOT / "backups"

            if backups_dir.exists():
                backup_files = list(backups_dir.glob("backup_*.db")) + list(backups_dir.glob("backup_*.zip"))

                for backup_file in sorted(backup_files, key=lambda x: x.stat().st_mtime, reverse=True):
                    # معلومات الملف
                    stat = backup_file.stat()
                    size = self.format_file_size(stat.st_size)
                    date = datetime.fromtimestamp(stat.st_mtime).strftime('%Y-%m-%d %H:%M')

                    self.backups_tree.insert('', 'end', values=(
                        backup_file.name,
                        date,
                        size,
                        str(backup_file)
                    ))

            # البحث في مجلد النسخ الاحتياطية المخصص
            backup_path = Path(self.backup_path_var.get())
            if backup_path.exists() and backup_path != backups_dir:
                backup_files = list(backup_path.glob("backup_*.zip"))

                for backup_file in sorted(backup_files, key=lambda x: x.stat().st_mtime, reverse=True):
                    stat = backup_file.stat()
                    size = self.format_file_size(stat.st_size)
                    date = datetime.fromtimestamp(stat.st_mtime).strftime('%Y-%m-%d %H:%M')

                    self.backups_tree.insert('', 'end', values=(
                        backup_file.name,
                        date,
                        size,
                        str(backup_file)
                    ))

        except Exception as e:
            pass
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحديث قائمة النسخ الاحتياطية: {e}")

    def browse_restore_file(self):
        """تصفح ملف للاستعادة"""
        file_path = filedialog.askopenfilename(
            title="اختر ملف النسخة الاحتياطية",
            filetypes=[
                ("ملفات النسخ الاحتياطي", "*.db *.zip"),
                ("قاعدة البيانات", "*.db"),
                ("ملفات مضغوطة", "*.zip"),
                ("جميع الملفات", "*.*")
            ]
        )

        if file_path:
            # إضافة الملف للقائمة
            file_path_obj = Path(file_path)
            stat = file_path_obj.stat()
            size = self.format_file_size(stat.st_size)
            date = datetime.fromtimestamp(stat.st_mtime).strftime('%Y-%m-%d %H:%M')

            # إضافة للقائمة وتحديده
            item = self.backups_tree.insert('', 0, values=(
                file_path_obj.name,
                date,
                size,
                file_path
            ))
            self.backups_tree.selection_set(item)

    def restore_backup(self):
        """استعادة النسخة الاحتياطية"""
        selection = self.backups_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار نسخة احتياطية للاستعادة")
            return

        # الحصول على مسار الملف
        item = self.backups_tree.item(selection[0])
        backup_path = item['values'][3]

        # تأكيد الاستعادة
        result = messagebox.askyesno(
            "تأكيد الاستعادة",
            "هل أنت متأكد من استعادة هذه النسخة الاحتياطية؟\n"
            "سيتم استبدال البيانات الحالية."
        )

        if result:
            # تشغيل الاستعادة في خيط منفصل
            thread = threading.Thread(target=self._restore_backup_thread, args=(backup_path,))
            thread.daemon = True
            thread.start()

    def _restore_backup_thread(self, backup_path):
        """تنفيذ الاستعادة في خيط منفصل"""
        try:
            self.update_status("بدء الاستعادة...", 0)

            backup_file = Path(backup_path)

            if backup_file.suffix.lower() == '.db':
                # استعادة قاعدة البيانات فقط
                success, message = self.app_core.restore_database(backup_path)
                if success:
                    self.update_status("تم استعادة قاعدة البيانات بنجاح", 100)
                else:
                    self.update_status(f"خطأ في الاستعادة: {message}", 0)

            elif backup_file.suffix.lower() == '.zip':
                # استعادة من ملف مضغوط
                from config.settings import PROJECT_ROOT, DATABASE_PATH

                with zipfile.ZipFile(backup_path, 'r') as zipf:
                    # استعادة قاعدة البيانات
                    if 'database/accounting.db' in zipf.namelist():
                        self.update_status("استعادة قاعدة البيانات...", 33)
                        zipf.extract('database/accounting.db', PROJECT_ROOT / "temp")
                        temp_db = PROJECT_ROOT / "temp" / "database" / "accounting.db"

                        # إنشاء نسخة احتياطية من قاعدة البيانات الحالية
                        if DATABASE_PATH.exists():
                            backup_current = DATABASE_PATH.parent / f"backup_before_restore_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db"
                            shutil.copy2(DATABASE_PATH, backup_current)

                        # استبدال قاعدة البيانات
                        shutil.move(temp_db, DATABASE_PATH)

                    # استعادة الإعدادات
                    config_files = [f for f in zipf.namelist() if f.startswith('config/')]
                    if config_files:
                        self.update_status("استعادة الإعدادات...", 66)
                        for config_file in config_files:
                            zipf.extract(config_file, PROJECT_ROOT)

                    # استعادة التقارير
                    report_files = [f for f in zipf.namelist() if f.startswith('reports/')]
                    if report_files:
                        self.update_status("استعادة التقارير...", 90)
                        for report_file in report_files:
                            zipf.extract(report_file, PROJECT_ROOT)

                # تنظيف المجلد المؤقت
                temp_dir = PROJECT_ROOT / "temp"
                if temp_dir.exists():
                    shutil.rmtree(temp_dir)

                self.update_status("تم استعادة النسخة الاحتياطية بنجاح", 100)

            # عرض رسالة نجاح
            self.window.after(0, lambda: messagebox.showinfo(
                "نجح",
                "تم استعادة النسخة الاحتياطية بنجاح.\nيُنصح بإعادة تشغيل البرنامج."
            ))

        except Exception as e:
            pass
        except Exception as e:
            error_msg = f"خطأ في استعادة النسخة الاحتياطية: {e}"
            self.update_status(error_msg, 0)
            self.window.after(0, lambda: messagebox.showerror("خطأ", error_msg))

    def toggle_auto_backup(self):
        """تبديل النسخ الاحتياطي التلقائي"""
        enabled = self.auto_backup_enabled.get()
        self.app_core.set_setting('auto_backup', enabled)

        if enabled:
            messagebox.showinfo("تم التفعيل", "تم تفعيل النسخ الاحتياطي التلقائي")
        else:
            messagebox.showinfo("تم الإلغاء", "تم إلغاء النسخ الاحتياطي التلقائي")

    def save_schedule_settings(self):
        """حفظ إعدادات الجدولة"""
        try:
            interval = int(self.backup_interval_var.get())
            keep_count = int(self.keep_backups_var.get())

            if interval < 1:
                messagebox.showerror("خطأ", "فترة النسخ الاحتياطي يجب أن تكون أكبر من 0")
                return

            if keep_count < 1:
                messagebox.showerror("خطأ", "عدد النسخ المحفوظة يجب أن يكون أكبر من 0")
                return

            # حفظ الإعدادات
            self.app_core.set_setting('backup_interval', interval)
            self.app_core.set_setting('keep_backups', keep_count)

            messagebox.showinfo("نجح", "تم حفظ إعدادات الجدولة بنجاح")

        except ValueError:
            pass
        except ValueError:
            messagebox.showerror("خطأ", "يرجى إدخال أرقام صحيحة")

    def update_status(self, message, progress):
        """تحديث شريط الحالة والتقدم"""
        def update():
            self.status_var.set(message)
            self.progress_var.set(progress / 100)

        if self.window:
            self.window.after(0, update)

    def format_file_size(self, size_bytes):
        """تنسيق حجم الملف"""
        if size_bytes == 0:
            return "0 B"

        size_names = ["B", "KB", "MB", "GB"]
        import math
from ui.window_utils import configure_window_fullscreen
from pathlib import Path
        i = int(math.floor(math.log(size_bytes, 1024)))
        p = math.pow(1024, i)
        s = round(size_bytes / p, 2)
        return f"{s} {size_names[i]}"

    def close_window(self):
        """إغلاق النافذة"""
        self.if window and hasattr(window, "destroy"):
    if window and hasattr(window, "destroy"):
    window.destroy()