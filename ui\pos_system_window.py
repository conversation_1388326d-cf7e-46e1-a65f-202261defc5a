# -*- coding: utf-8 -*-
"""
نافذة نقطة البيع POS - واجهة سريعة وعملية للمبيعات
"""

import os
import tkinter as tk
from tkinter import messagebox, ttk
from datetime import datetime, date
import customtkinter as ctk

from themes.modern_theme import MODERN_COLORS, FONTS, DIMENSIONS
from ui.window_utils import configure_window_fullscreen


class POSSystemWindow:
    """نافذة نقطة البيع POS مع واجهة سريعة وعملية"""

    def __init__(self, parent=None):
        self.parent = parent
        self.window = None
        self.current_user = getattr(parent, 'current_user', {'role': 'admin', 'username': 'admin'})

        # بيانات السلة
        self.cart_items = []
        self.cart_total = 0.0
        self.selected_customer = "عميل نقدي"

        # متغيرات الواجهة
        self.cart_tree = None
        self.total_label = None
        self.barcode_entry = None
        self.quantity_entry = None

        self.create_window()

    def create_window(self):
        """إنشاء نافذة نقطة البيع"""
        self.window = ctk.CTkToplevel()
        self.window.title("🛒 نقطة البيع POS - برنامج ست الكل للمحاسبة")

        # تكوين النافذة
        configure_window_fullscreen(self.window)
        self.window.configure(fg_color=MODERN_COLORS['background'])

        # إنشاء الإطار الرئيسي
        main_frame = ctk.CTkFrame(self.window, fg_color="transparent")
        main_frame.pack(fill="both", expand=True, padx=10, pady=10)

        # إنشاء الأقسام الرئيسية
        self.create_pos_layout(main_frame)

    def create_pos_layout(self, parent):
        """إنشاء تخطيط نقطة البيع"""
        # القسم العلوي - شريط الأدوات
        self.create_toolbar(parent)

        # القسم الرئيسي - مقسم إلى جزأين
        content_frame = ctk.CTkFrame(parent, fg_color="transparent")
        content_frame.pack(fill="both", expand=True, pady=(10, 0))

        # الجانب الأيسر - منطقة الإدخال والمنتجات
        left_panel = ctk.CTkFrame(content_frame, fg_color=MODERN_COLORS['surface'])
        left_panel.pack(side="left", fill="both", expand=True, padx=(0, 5))

        self.create_input_section(left_panel)
        self.create_products_grid(left_panel)

        # الجانب الأيمن - السلة والدفع
        right_panel = ctk.CTkFrame(content_frame, width=400, fg_color=MODERN_COLORS['surface'])
        right_panel.pack(side="right", fill="y", padx=(5, 0))
        right_panel.pack_propagate(False)

        self.create_cart_section(right_panel)
        self.create_payment_section(right_panel)

    def create_toolbar(self, parent):
        """إنشاء شريط الأدوات العلوي"""
        toolbar = ctk.CTkFrame(parent, height=70, fg_color=MODERN_COLORS['info'])
        toolbar.pack(fill="x", pady=(0, 10))
        toolbar.pack_propagate(False)

        # العنوان
        title_frame = ctk.CTkFrame(toolbar, fg_color="transparent")
        title_frame.pack(side="right", fill="y", padx=20, pady=10)

        title = ctk.CTkLabel(
            title_frame,
            text="🛒 نقطة البيع السريعة",
            font=("Cairo", 20, "bold"),
            text_color="white"
        )
        title.pack(anchor="e")

        # معلومات الجلسة
        session_info = ctk.CTkLabel(
            title_frame,
            text=f"المستخدم: {self.current_user.get('username', 'غير محدد')} | التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M')}",
            font=("Cairo", 12),
            text_color="white"
        )
        session_info.pack(anchor="e")

        # أزرار سريعة
        buttons_frame = ctk.CTkFrame(toolbar, fg_color="transparent")
        buttons_frame.pack(side="left", fill="y", padx=20, pady=10)

        quick_buttons = [
            ("🔄", "جلسة جديدة", self.new_session, MODERN_COLORS['success']),
            ("💾", "حفظ مؤقت", self.save_temp, MODERN_COLORS['warning']),
            ("⚙️", "إعدادات", self.pos_settings, MODERN_COLORS['secondary'])
        ]

        for icon, text, command, color in quick_buttons:
            btn = ctk.CTkButton(
                buttons_frame,
                text=f"{icon} {text}",
                font=("Cairo", 11),
                fg_color=color,
                width=100,
                height=30,
                command=command
            )
            btn.pack(side="left", padx=5)

    def create_input_section(self, parent):
        """إنشاء قسم الإدخال السريع"""
        input_frame = ctk.CTkFrame(parent, height=120, fg_color="white")
        input_frame.pack(fill="x", padx=15, pady=15)
        input_frame.pack_propagate(False)

        # عنوان القسم
        title = ctk.CTkLabel(
            input_frame,
            text="⚡ إدخال سريع",
            font=("Cairo", 16, "bold"),
            text_color=MODERN_COLORS['text_primary']
        )
        title.pack(pady=(10, 5))

        # صف الإدخال
        input_row = ctk.CTkFrame(input_frame, fg_color="transparent")
        input_row.pack(fill="x", padx=20, pady=10)

        # إدخال الباركود
        barcode_label = ctk.CTkLabel(input_row, text="الباركود/الكود:", font=("Cairo", 12))
        barcode_label.pack(side="right", padx=(0, 10))

        self.barcode_entry = ctk.CTkEntry(
            input_row,
            placeholder_text="امسح الباركود أو أدخل كود المنتج",
            font=("Cairo", 12),
            width=250
        )
        self.barcode_entry.pack(side="right", padx=5)
        self.barcode_entry.bind("<Return>", self.add_item_by_barcode)

        # إدخال الكمية
        qty_label = ctk.CTkLabel(input_row, text="الكمية:", font=("Cairo", 12))
        qty_label.pack(side="right", padx=(20, 10))

        self.quantity_entry = ctk.CTkEntry(
            input_row,
            placeholder_text="1",
            font=("Cairo", 12),
            width=80
        )
        self.quantity_entry.pack(side="right", padx=5)
        self.quantity_entry.insert(0, "1")

        # زر الإضافة
        add_btn = ctk.CTkButton(
            input_row,
            text="➕ إضافة",
            font=("Cairo", 12),
            fg_color=MODERN_COLORS['success'],
            width=100,
            command=self.add_item_by_barcode
        )
        add_btn.pack(side="left", padx=10)

    def create_products_grid(self, parent):
        """إنشاء شبكة المنتجات السريعة"""
        products_frame = ctk.CTkFrame(parent, fg_color="white")
        products_frame.pack(fill="both", expand=True, padx=15, pady=(0, 15))

        # عنوان القسم
        title = ctk.CTkLabel(
            products_frame,
            text="🛍️ المنتجات السريعة",
            font=("Cairo", 16, "bold"),
            text_color=MODERN_COLORS['text_primary']
        )
        title.pack(pady=10)

        # إطار الشبكة
        grid_frame = ctk.CTkFrame(products_frame, fg_color="transparent")
        grid_frame.pack(fill="both", expand=True, padx=20, pady=(0, 20))

        # منتجات تجريبية
        sample_products = [
            ("كوكا كولا", "5.00", "#FF6B6B"),
            ("بيبسي", "4.50", "#4ECDC4"),
            ("ماء صافي", "1.00", "#45B7D1"),
            ("عصير برتقال", "3.00", "#FFA07A"),
            ("شيبس", "2.50", "#98D8C8"),
            ("شوكولاتة", "6.00", "#F7DC6F"),
            ("بسكويت", "4.00", "#BB8FCE"),
            ("علكة", "1.50", "#85C1E9")
        ]

        # إنشاء أزرار المنتجات
        for i, (name, price, color) in enumerate(sample_products):
            row = i // 4
            col = i % 4

            product_btn = ctk.CTkButton(
                grid_frame,
                text=f"{name}\n{price} ر.س",
                font=("Cairo", 11),
                fg_color=color,
                hover_color=self.darken_color(color),
                width=120,
                height=80,
                command=lambda n=name, p=price: self.add_quick_product(n, p)
            )
            product_btn.grid(row=row, column=col, padx=5, pady=5, sticky="ew")
            grid_frame.grid_columnconfigure(col, weight=1)

    def create_cart_section(self, parent):
        """إنشاء قسم السلة"""
        # عنوان السلة
        cart_header = ctk.CTkFrame(parent, height=60, fg_color=MODERN_COLORS['success'])
        cart_header.pack(fill="x", padx=15, pady=15)
        cart_header.pack_propagate(False)

        cart_title = ctk.CTkLabel(
            cart_header,
            text="🛒 سلة المشتريات",
            font=("Cairo", 16, "bold"),
            text_color="white"
        )
        cart_title.pack(expand=True)

        # جدول السلة
        cart_frame = ctk.CTkFrame(parent, fg_color="white")
        cart_frame.pack(fill="both", expand=True, padx=15, pady=(0, 10))

        # إنشاء Treeview للسلة
        columns = ("item", "qty", "price", "total")
        column_names = ("المنتج", "الكمية", "السعر", "الإجمالي")

        self.cart_tree = ttk.Treeview(cart_frame, columns=columns, show="headings", height=8)

        # تكوين الأعمدة
        for col, name in zip(columns, column_names):
            self.cart_tree.heading(col, text=name)
            if col == "item":
                self.cart_tree.column(col, width=150, anchor="e")
            else:
                self.cart_tree.column(col, width=80, anchor="center")

        # شريط التمرير
        cart_scrollbar = ttk.Scrollbar(cart_frame, orient="vertical", command=self.cart_tree.yview)
        self.cart_tree.configure(yscrollcommand=cart_scrollbar.set)

        # تخطيط الجدول
        self.cart_tree.pack(side="left", fill="both", expand=True, padx=10, pady=10)
        cart_scrollbar.pack(side="right", fill="y", pady=10)

        # ربط الأحداث
        self.cart_tree.bind("<Delete>", self.remove_from_cart)
        self.cart_tree.bind("<Double-1>", self.edit_cart_item)

    def create_payment_section(self, parent):
        """إنشاء قسم الدفع"""
        payment_frame = ctk.CTkFrame(parent, height=200, fg_color="white")
        payment_frame.pack(fill="x", padx=15, pady=(0, 15))
        payment_frame.pack_propagate(False)

        # الإجمالي
        total_frame = ctk.CTkFrame(payment_frame, fg_color=MODERN_COLORS['primary'])
        total_frame.pack(fill="x", padx=10, pady=10)

        self.total_label = ctk.CTkLabel(
            total_frame,
            text="الإجمالي: 0.00 ر.س",
            font=("Cairo", 18, "bold"),
            text_color="white"
        )
        self.total_label.pack(pady=10)

        # أزرار الدفع
        payment_buttons_frame = ctk.CTkFrame(payment_frame, fg_color="transparent")
        payment_buttons_frame.pack(fill="x", padx=10, pady=10)

        payment_methods = [
            ("💵", "نقدي", self.cash_payment, MODERN_COLORS['success']),
            ("💳", "شبكة", self.card_payment, MODERN_COLORS['info']),
            ("📱", "تحويل", self.transfer_payment, MODERN_COLORS['warning'])
        ]

        for icon, text, command, color in payment_methods:
            btn = ctk.CTkButton(
                payment_buttons_frame,
                text=f"{icon}\n{text}",
                font=("Cairo", 11),
                fg_color=color,
                width=100,
                height=50,
                command=command
            )
            btn.pack(side="left", padx=5, expand=True, fill="x")

        # أزرار إضافية
        extra_buttons_frame = ctk.CTkFrame(payment_frame, fg_color="transparent")
        extra_buttons_frame.pack(fill="x", padx=10, pady=(0, 10))

        clear_btn = ctk.CTkButton(
            extra_buttons_frame,
            text="🗑️ مسح الكل",
            font=("Cairo", 11),
            fg_color=MODERN_COLORS['error'],
            width=120,
            command=self.clear_cart
        )
        clear_btn.pack(side="left", padx=5)

        hold_btn = ctk.CTkButton(
            extra_buttons_frame,
            text="⏸️ تعليق",
            font=("Cairo", 11),
            fg_color=MODERN_COLORS['secondary'],
            width=120,
            command=self.hold_transaction
        )
        hold_btn.pack(side="right", padx=5)

    def darken_color(self, color):
        """تغميق اللون للتمرير"""
        # تحويل بسيط للون أغمق
        return color.replace("FF", "DD").replace("F7", "D5").replace("98", "78")

    def add_item_by_barcode(self, event=None):
        """إضافة منتج بالباركود"""
        barcode = self.barcode_entry.get().strip()
        quantity = self.quantity_entry.get().strip() or "1"

        if not barcode:
            messagebox.showwarning("تحذير", "يرجى إدخال الباركود أو كود المنتج")
            return

        try:
            qty = float(quantity)
            if qty <= 0:
                raise ValueError("الكمية يجب أن تكون أكبر من صفر")
        except ValueError as e:
            messagebox.showerror("خطأ", f"كمية غير صحيحة: {str(e)}")
            return

        # البحث عن المنتج (محاكاة)
        # في التطبيق الحقيقي، سيتم البحث في قاعدة البيانات
        sample_product = {
            'name': f'منتج {barcode}',
            'price': 10.00,
            'available_qty': 100
        }

        self.add_to_cart(sample_product['name'], qty, sample_product['price'])

        # مسح الحقول
        self.barcode_entry.delete(0, tk.END)
        self.quantity_entry.delete(0, tk.END)
        self.quantity_entry.insert(0, "1")
        self.barcode_entry.focus()

    def add_quick_product(self, name, price):
        """إضافة منتج سريع"""
        try:
            price_float = float(price)
            quantity = float(self.quantity_entry.get() or "1")
            self.add_to_cart(name, quantity, price_float)
        except ValueError:
            messagebox.showerror("خطأ", "خطأ في بيانات المنتج")

    def add_to_cart(self, name, quantity, price):
        """إضافة منتج إلى السلة"""
        total = quantity * price

        # إضافة إلى قائمة السلة
        item = {
            'name': name,
            'quantity': quantity,
            'price': price,
            'total': total
        }
        self.cart_items.append(item)

        # إضافة إلى الجدول
        self.cart_tree.insert("", "end", values=(name, f"{quantity:.1f}", f"{price:.2f}", f"{total:.2f}"))

        # تحديث الإجمالي
        self.update_cart_total()

    def update_cart_total(self):
        """تحديث إجمالي السلة"""
        self.cart_total = sum(item['total'] for item in self.cart_items)
        self.total_label.configure(text=f"الإجمالي: {self.cart_total:.2f} ر.س")

    def remove_from_cart(self, event=None):
        """حذف منتج من السلة"""
        selected = self.cart_tree.selection()
        if selected:
            # حذف من الجدول
            item_index = self.cart_tree.index(selected[0])
            self.cart_tree.delete(selected[0])

            # حذف من القائمة
            if 0 <= item_index < len(self.cart_items):
                del self.cart_items[item_index]

            self.update_cart_total()

    def edit_cart_item(self, event=None):
        """تعديل منتج في السلة"""
        messagebox.showinfo("قريباً", "تعديل المنتجات قيد التطوير")

    def clear_cart(self):
        """مسح السلة"""
        if messagebox.askyesno("تأكيد", "هل تريد مسح جميع المنتجات من السلة؟"):
            self.cart_items.clear()
            for item in self.cart_tree.get_children():
                self.cart_tree.delete(item)
            self.update_cart_total()

    def cash_payment(self):
        """الدفع النقدي"""
        if not self.cart_items:
            messagebox.showwarning("تحذير", "السلة فارغة")
            return
        messagebox.showinfo("قريباً", "نظام الدفع النقدي قيد التطوير")

    def card_payment(self):
        """الدفع بالشبكة"""
        if not self.cart_items:
            messagebox.showwarning("تحذير", "السلة فارغة")
            return
        messagebox.showinfo("قريباً", "نظام الدفع بالشبكة قيد التطوير")

    def transfer_payment(self):
        """الدفع بالتحويل"""
        if not self.cart_items:
            messagebox.showwarning("تحذير", "السلة فارغة")
            return
        messagebox.showinfo("قريباً", "نظام الدفع بالتحويل قيد التطوير")

    def hold_transaction(self):
        """تعليق المعاملة"""
        messagebox.showinfo("قريباً", "تعليق المعاملات قيد التطوير")

    def new_session(self):
        """جلسة جديدة"""
        if self.cart_items and not messagebox.askyesno("تأكيد", "هل تريد بدء جلسة جديدة؟ سيتم فقدان البيانات الحالية."):
            return
        self.clear_cart()

    def save_temp(self):
        """حفظ مؤقت"""
        messagebox.showinfo("قريباً", "الحفظ المؤقت قيد التطوير")

    def pos_settings(self):
        """إعدادات نقطة البيع"""
        messagebox.showinfo("قريباً", "إعدادات نقطة البيع قيد التطوير")


def main():
    """تشغيل النافذة للاختبار"""
    root = ctk.CTk()
    root.withdraw()

    app = POSSystemWindow()
    root.mainloop()


if __name__ == "__main__":
    main()
